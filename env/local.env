STAGE=dev
PORT=3000
INTERNAL_GRPC_PORT=5001
JWT_SECRET=0Pl0f4hIQ1cnwcD8T4R1Wa4a15rB5E4kj0vLW0ddk7A0
JWT_REFRESH_SECRET=0Pl0f4hIQ1cnwcD8T4R1Wa4a15rB5E4kj0vLW0ddk7A1
ENCRYPT_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmMW7U1Z64ocVtsA2B+D6\nIGh41AsGZ5hRG7IJYuLbgiQeGDiywB+TSUEIqgjl80H2679gbP5p8dpWzALmPPnN\nRufGjnL5u90itzDgOJ8aIb1ecDQNQA9pMkL/3mZxOG4h9RA73Q6/ssayOoJ/Ok6+\nuyxqiLqNixLX6lL6DAa6UBn0wJATYwiCkA8NsoKR387jUc4OUekC/4gFNnWN1GCg\ndAKlyuY/VMRHQp2SS8Zit4ztdVCJje+4nkvQP+zYLV3VYvJHVCVQ4RuR2jaA/Ss3\nnAyCahXxjXXjjlTV7yQVS95t4hPOju9ti6kng6zOFs9A/yvLq14AxBkQTO+8e0c1\nMQIDAQAB\n-----END PUBLIC KEY-----\n"

# PG config
# ------------------------------------------------------------------
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=dev
POSTGRES_PASS=dev
POSTGRES_DB_NAME=xbit

# Redis config
# ------------------------------------------------------------------
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASS=dev
REDIS_DB=0

# Telegram bot config
# ------------------------------------------------------------------
TELEGRAM_BOT_DOMAIN=sunbird-apparent-recently.ngrok-free.app
TELEGRAM_BOT_WEBHOOK=5woDRWa7sWt1D3cOQUVMWlJlR0zwacizIQCCGkVJgQE0
TELEGRAM_BOT_AUTH_TOKEN=**********************************************
TELEGRAM_LOGIN_URL=https://unstable.xbit.live/telegram-auth

# Google OAuth Configuration
# ------------------------------------------------------------------
GOOGLE_CLIENT_ID=531338041207-l62jm1ak8cm2doku92j09oj8lckp1s3g.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

#Telegram tranding bot config
#-------------------------------------------------------------------
TELEGRAM_BOT_SNIPER_TOKEN=

#Telegram tranding bot config
#-------------------------------------------------------------------
TELEGRAM_BOT_SNIPER_TOKEN=

# Wallet service config
# ------------------------------------------------------------------
WALLET_SERVICE_HOST=*********
WALLET_SERVICE_PORT=8080
WALLET_SERVICE_APIKEY=9Em1eYenA6C40lwyooalse0B1JQ0zRWcIfPvUByp7Uw0

#ETH
# ------------------------------------------------------------------
INFURA_RPC_URL=<no value>
TRON_GRID_API_KEY=<no value>

#EMQX
EMQX_PROTOCOL=mqtt
EMQX_HOST=0.0.0.0
EMQX_PORT=1883
EMQX_USER=
EMQX_PASS=

#XBIT LANDING PAGE
LANDING_PAGE_URL=""

MEME_BASE_URL=https://unstable-api.xbit.live/api/meme/graphql
TRANDING_BASE_URL=https://unstable-api.xbit.live/api/trading/query

REFERRAL_ADDRESS=https://t.me/zbit_unstable_sniper_solana_bot
WEBSTITE_ADDRESS=https://unstable.xbit.live/meme/discover

TOKEN_DETAILS=https://unstable.xbit.live/meme/token/

ACCESS_TOKEN_EXPIRES_IN=1d
REFRESH_TOKEN_EXPIRES_IN=30d

NATS_URL=localhost:4222
NATS_AUTH_TOKEN=
NATS_USER=
NATS_PASS=

*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
TURNKEY_API_BASE_URL=https://api.turnkey.com
TURNKEY_API_PRIVATE_KEY=e1b69dfab744f5982a7fe0d1339c2479b6d82d261ba6d91619c3d247aaf2e9c1
TURNKEY_API_PUBLIC_KEY=028c8535ed4ba2b32f0066e9fe794b7fa5b7b32b707b2bdffb488e727684b31cff
TURNKEY_ORGANIZATION_ID=58b93d40-2540-4f00-9824-8eca60069376

import { TurnkeyService } from '@lib/internal/turnkey';
import { Injectable } from '@nestjs/common';
import {
    ApproveWithdrawRequest,
    ApproveWithdrawResponse,
    ChainType as RequestChainType,
    SignUserTransactionEvmRequest,
    SignUserTransactionEvmResponse,
    SignUserTransactionRequest,
    SignUserTransactionResponse,
} from '@protogen/user/v1/signing';
import { parseEther, Transaction } from 'ethers';
import { ChainType } from 'libs/internal/users/entities/user-managed-wallet.entity';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';

@Injectable()
export class UserSigningService {
    constructor(
        private readonly turnKeyService: TurnkeyService,
        @InjectPinoLogger()
        private readonly logger: PinoLogger,
    ) {}

    async signTransaction(request: SignUserTransactionRequest): Promise<SignUserTransactionResponse> {
        let chainType = ChainType.EVM;
        switch (request.chain) {
            case RequestChainType.EVM:
                chainType = ChainType.EVM;
                break;
            case RequestChainType.ARB:
                chainType = ChainType.ARB;
                break;
            case RequestChainType.SOLANA:
                chainType = ChainType.SOLANA;
                break;
            case RequestChainType.TRON:
                chainType = ChainType.TRON;
                break;
            default:
                break;
        }
        const signedTransaction = await this.turnKeyService.signUserTransaction(
            request.userId,
            chainType,
            request.unsignedTransaction,
            request.address,
        );

        return {
            signedTransaction,
        };
    }

    async approveWithdraw(request: ApproveWithdrawRequest): Promise<ApproveWithdrawResponse> {
        const signedTransaction = await this.turnKeyService.withdrawTransaction(request);

        return {
            signedTransaction,
        };
    }

    async signEvmTransaction(request: SignUserTransactionEvmRequest): Promise<SignUserTransactionEvmResponse> {
        this.logger.info({ request });
        let chainType = ChainType.EVM;
        let chainId = 1;
        switch (request.chain) {
            case RequestChainType.EVM:
                chainType = ChainType.EVM;
                chainId = 1; // Default to Ethereum mainnet
                break;
            case RequestChainType.BSC:
                chainType = ChainType.EVM;
                chainId = 56; // Binance Smart Chain mainnet
                break;
            case RequestChainType.POLYGON:
                chainType = ChainType.EVM;
                chainId = 137; // Polygon mainnet
                break;
            case RequestChainType.ARB:
                chainType = ChainType.ARB;
                chainId = 42161; // Arbitrum mainnet
                break;
            case RequestChainType.SOLANA:
                chainType = ChainType.SOLANA;
                break;
            case RequestChainType.TRON:
                chainType = ChainType.TRON;
                chainId = 1; // Tron mainnet
                break;
            default:
                break;
        }

        const tx = new Transaction();
        tx.data = request.data;
        tx.to = request.to;
        // check value is hex
        tx.value = request.value ? BigInt(request.value) : BigInt(0);
        tx.maxFeePerGas = request.maxFeePerGas ? BigInt(request.maxFeePerGas) : null;
        tx.gasPrice = request.gasPrice ? BigInt(request.gasPrice) : null;
        tx.maxPriorityFeePerGas = request.maxPriorityFeePerGas ? BigInt(request.maxPriorityFeePerGas) : null;
        tx.gasPrice = request.gasPrice ? BigInt(request.gasPrice) : null;
        tx.nonce = BigInt(parseInt(request.nonce));
        tx.chainId = chainId;

        if (request.gasLimit) {
            tx.gasLimit = BigInt(request.gasLimit);
        }

        const signedTransaction = await this.turnKeyService.signUserTransaction(
            request.userId,
            chainType,
            tx.unsignedSerialized,
            request.from,
        );

        return {
            ...request,
            signedTransaction: '0x' + signedTransaction,
        };
    }
}
